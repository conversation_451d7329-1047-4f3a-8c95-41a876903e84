import json
import logging
import os
import re
import shutil
import types
from datetime import datetime
import time
import itertools
import uuid
import sys
from pathlib import Path

import cv2
import numpy as np

# Chrome driver yönetimi için yeni importlar
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
import threading
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

# Chrome driver yönetimi için global lock
_chrome_driver_lock = threading.Lock()

def create_chrome_driver_for_account(account_username, headless=True, max_retries=3):
    """
    Sadece portable Chromium + uygun Chromedriver ile başlatır.
    Sistem Chrome'unu ve diğer tüm yolları yok sayar.
    """
    with _chrome_driver_lock:
        options = webdriver.ChromeOptions()
        if headless:
            options.add_argument('--headless=new')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-notifications')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--remote-debugging-port=0')
        options.add_argument('--lang=en-US')
        options.add_argument('--disable-blink-features=AutomationControlled')

        # Her kullanıcıya özel profil klasörü
        base_dir = os.path.dirname(os.path.abspath(__file__))
        profile_base = os.path.join(base_dir, f"chrome_profile_{account_username}")
        os.makedirs(profile_base, exist_ok=True)
        options.add_argument(f'--user-data-dir={profile_base}')

        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 2
        }
        options.add_experimental_option("prefs", prefs)

        # Portable Chromium binary ve chromedriver yolunu al
        from download import setup_portable_chromium, prepare_unique_chromedriver
        binary_path, driver_path = setup_portable_chromium()
        options.binary_location = binary_path

        # Kilitlenme sorununu önlemek için tek kullanımlık kopya
        driver_path = prepare_unique_chromedriver(driver_path)

        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=options)

        logging.info(f"Portable Chrome driver başarıyla oluşturuldu: {account_username}")
        return driver

def dummy_duration(path):
    """
    Get video duration using OpenCV with improved accuracy and fallback methods.
    """
    try:
        cap = cv2.VideoCapture(path)
        if not cap.isOpened():
            # Try alternative method with FFprobe if available
            return get_duration_with_ffprobe(path)

        # Method 1: Use CAP_PROP_POS_MSEC for more accurate duration
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)

        # Try to get duration directly from video properties
        duration_ms = cap.get(cv2.CAP_PROP_POS_MSEC)

        # Calculate duration using frame count and FPS
        if fps > 0 and frame_count > 0:
            calculated_duration = frame_count / fps
        else:
            calculated_duration = 30  # Default fallback

        cap.release()

        # Return the calculated duration (more reliable than direct property)
        return max(calculated_duration, 1.0)  # Ensure minimum 1 second

    except Exception as e:
        logging.debug(f"OpenCV duration detection failed for {path}: {e}")
        # Fallback to FFprobe if available
        return get_duration_with_ffprobe(path)


def get_duration_with_ffprobe(path):
    """
    Fallback method to get video duration using FFprobe if available.
    """
    try:
        import subprocess
        from download import download_ffmpeg

        ffmpeg_dir = download_ffmpeg()
        if not ffmpeg_dir:
            return 30  # Default fallback

        ffprobe_exe = os.path.join(ffmpeg_dir, "ffprobe.exe")
        if not os.path.exists(ffprobe_exe):
            return 30  # Default fallback

        cmd = [
            ffprobe_exe,
            "-v", "quiet",
            "-show_entries", "format=duration",
            "-of", "csv=p=0",
            path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and result.stdout.strip():
            duration = float(result.stdout.strip())
            return max(duration, 1.0)  # Ensure minimum 1 second

    except Exception as e:
        logging.debug(f"FFprobe duration detection failed for {path}: {e}")

    return 30  # Final fallback


def setup_logging() -> logging.Logger:
    """
    Loglama sistemini ayarlar ve **her seferinde** kök logger'ı
    yeniden yapılandırmak için Python 3.8+'deki `force=True` parametresini
    kullanır. Böylece PyCharm / IDE çıktısında log'ların kaybolma
    problemi ortadan kalkar.
    """
    base_dir   = Path(__file__).resolve().parent
    videos_dir = base_dir / "videos"
    videos_dir.mkdir(exist_ok=True)

    log_file = videos_dir / "social_downloader.log"

    logging.basicConfig(
        level    = logging.INFO,
        format   = "%(asctime)s - %(levelname)s - %(message)s",
        handlers = [
            logging.FileHandler(log_file, encoding="utf-8"),
            # IDE'lerin çoğu stdout'u daha iyi işler
            logging.StreamHandler(sys.stdout),
        ],
        force=True          #  ← kritik: daha önce yapılandırılmış olsa bile üzerine yazar
    )

    # Tek tip isimle uygulama logger'ı döndürmek pratik oluyor
    return logging.getLogger("social_downloader")


def create_directory(path):
    """İndirilen dosyaların kaydedileceği dizini oluşturur"""
    if not os.path.exists(path):
        os.makedirs(path)
        logging.info(f"Dizin oluşturuldu: {path}")


def clean_temp_files(temp_dir, retries=5):
    for attempt in range(retries):
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                logging.info(f"Geçici dosyalar temizlendi: {temp_dir}")
                return
        except Exception as e:
            logging.error(f"Geçici dosyalar temizlenirken hata oluştu (deneme {attempt+1}): {str(e)}")
            time.sleep(0.5)
    logging.error(f"Geçici dosya silinemedi: {temp_dir}")


def protect_media_files(profile_json_path):
    """
    Henüz paylaşılmamış medya dosyalarını koruma listesine alır.
    JSON'daki tüm medya dosyalarını ve thumbnail'lerini korur.
    Shortcode-based naming için özel koruma sağlar.
    """
    protected_files = set()

    try:
        if os.path.exists(profile_json_path):
            with open(profile_json_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # Downloaded listesindeki tüm dosyaları koru
            for item in data.get("downloaded", []):
                if isinstance(item, dict):
                    # Legacy file_path support
                    if "file_path" in item:
                        file_path = item["file_path"]
                        if file_path and os.path.exists(file_path):
                            protected_files.add(file_path)

                            # Video dosyası için thumbnail'i de koru
                            if file_path.lower().endswith('.mp4'):
                                thumbnail_path = file_path.rsplit('.', 1)[0] + '.jpg'
                                if os.path.exists(thumbnail_path):
                                    protected_files.add(thumbnail_path)

                    # New shortcode-based paths
                    video_path = item.get("video_path")
                    if video_path and os.path.exists(video_path):
                        protected_files.add(video_path)

                    thumbnail_path = item.get("thumbnail_path")
                    if thumbnail_path and os.path.exists(thumbnail_path):
                        protected_files.add(thumbnail_path)

                    # For shortcode-based files, protect both video and thumbnail
                    shortcode = item.get("shortcode")
                    if shortcode:
                        # Get the directory from existing file paths or use default
                        base_dir = None
                        if video_path:
                            base_dir = os.path.dirname(video_path)
                        elif thumbnail_path:
                            base_dir = os.path.dirname(thumbnail_path)
                        elif "file_path" in item and item["file_path"]:
                            base_dir = os.path.dirname(item["file_path"])

                        if base_dir:
                            # Protect shortcode-based files
                            shortcode_video = os.path.join(base_dir, f"{shortcode}.mp4")
                            shortcode_thumbnail = os.path.join(base_dir, f"{shortcode}.jpg")

                            if os.path.exists(shortcode_video):
                                protected_files.add(shortcode_video)
                            if os.path.exists(shortcode_thumbnail):
                                protected_files.add(shortcode_thumbnail)

    except Exception as e:
        logging.warning(f"Medya koruma listesi oluşturulurken hata: {e}")

    return protected_files


def safe_file_delete(file_path, protected_files=None, allow_deletion=False):
    """
    Dosyayı güvenli bir şekilde siler. Korunan dosyalar silinmez.

    Args:
        file_path: Silinecek dosya yolu
        protected_files: Korunan dosyalar seti
        allow_deletion: True ise silme işlemine izin verir (post-sharing cleanup için)

    Returns:
        bool: Silme işlemi başarılı ise True
    """
    if protected_files is None:
        protected_files = set()

    # Absolute protection: Never delete Instagram/Twitter media files unless explicitly allowed
    if not allow_deletion:
        # Check if this is a media file that should be protected
        if (file_path.lower().endswith(('.mp4', '.jpg', '.jpeg', '.png', '.webm')) and
            ('instagramdownloaded' in file_path or 'twitterdownloaded' in file_path)):
            logging.debug(f"Media file protected from accidental deletion: {file_path}")
            return False

    if file_path in protected_files:
        logging.debug(f"Korunan dosya silinmedi: {file_path}")
        return False

    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logging.debug(f"Dosya güvenli bir şekilde silindi: {file_path}")
            return True
    except Exception as e:
        logging.warning(f"Dosya silinirken hata: {file_path} - {e}")

    return False


# Modül seviyesinde sayaç
_filename_counter = itertools.count(1)

def sanitize_filename(filename: str) -> str:
    """
    100 yıl sorunsuz çalışacak, tamamen sayısal + güvenli ASCII ad üretir.

    Çıktı ► YYYYMMDD-HHMMSS-NNNN [-kisa-anahtar]
        • tarih-saat : sıralanabilir
        • NNNN      : aynı saniye içinde bile benzersiz
        • kisa-anahtar (opsiyonel) : ilk 1 kelime ASCII-leştirilmiş özet (kısaltıldı)

    Dosya adı kısaltılır, yasak karakterler temizlenir.
    Windows'un 260 karakter sınırını aşmamak için daha kısa isimler üretir.
    """
    if not filename:
        filename = "file"

    # temel ascii özet
    ascii_name = (
        filename.encode("ascii", "ignore").decode()
        .replace(" ", "-")
        .replace("_", "-")
    )
    ascii_name = re.sub(r"[<>:\"/\\|?*#]", "", ascii_name)

    # Sadece ilk kelimeyi al ve maksimum 10 karakter ile sınırla
    words = re.findall(r"[A-Za-z0-9\-]+", ascii_name)
    if words:
        # Sadece ilk kelimeyi al ve 10 karakterle sınırla
        short_part = words[0][:10].lower()
    else:
        short_part = ""

    # Daha kısa zaman damgası formatı (yıl son 2 rakam)
    timestamp = datetime.now().strftime("%y%m%d-%H%M%S")
    unique_no = next(_filename_counter)
    core = f"{timestamp}-{unique_no:04d}"

    return f"{core}-{short_part}" if short_part else core


def create_video_thumbnail(video_path: str,
                           thumbnail_path: str | None = None,
                           wait_seconds: int = 30) -> str:
    """
    Video tamamlanmadan çağrılmışsa bile thumbnail üretmeyi dener.
    • .part uzantısını otomatik sıyırır
    • video dosyasının oluşmasını max `wait_seconds` bekler
    • var olan thumbnail'i asla ezmez
    """
    # .part ile bitiyorsa gerçek adı tahmin et
    if video_path.endswith(".part"):
        video_path = video_path[:-5]

    if not thumbnail_path:
        thumbnail_path = os.path.splitext(video_path)[0] + ".jpg"

    # Thumbnail zaten varsa
    if os.path.exists(thumbnail_path):
        return thumbnail_path

    # Video henüz flush edilmemiş olabilir → bekle
    timeout = time.time() + wait_seconds
    while not os.path.exists(video_path):
        if time.time() > timeout:
            logging.error(f"Thumbnail beklerken video bulunamadı: {video_path}")
            return thumbnail_path
        time.sleep(1)

    try:
        cap = cv2.VideoCapture(video_path)
        # Bazen video yazma tamamlandıktan hemen sonra açılmaz
        if not cap.isOpened():
            time.sleep(1)
            cap.open(video_path)

        ret, frame = cap.read()
        cap.release()
        if not ret:
            raise RuntimeError("İlk kare okunamadı")

        cv2.imwrite(thumbnail_path, frame)
        logging.info(f"Thumbnail oluşturuldu: {thumbnail_path}")
    except Exception as e:
        logging.error(f"Thumbnail oluşturulamadı ({video_path}): {e}")

    return thumbnail_path


html_content = r"""
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>3 Deck UI - Dark Theme (Modernized)</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
    .skeleton {
    background: linear-gradient(-90deg, #2b2b2b 0%, #3a3a3a 50%, #2b2b2b 100%);
    background-size: 400% 400%;
    animation: shimmer 1.2s ease-in-out infinite;
    border-radius: 10px;
    height: 20px;
    width: 80%;
    margin: 6px auto;
}


@keyframes shimmer {
    0% { background-position: 100% 0; }
    100% { background-position: -100% 0; }
}

.skeleton-container {
    display: flex;
    flex-direction: column;
    gap: 14px;
    padding: 10px 5px;
}


    /* ==== GENEL KUTU DİZİLİMİ ==== */
    .stats-grid {
    display: grid;
    /* Genişliğe göre otomatik kolon sayısı (120 px'den küçükse tek sütuna iner) */
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.stat-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #1e1e1e;
    padding: 10px 15px;
    border-radius: 10px;
}



    /* ==== TWITTER KUTULARI ==== */
    .twitter-stats .stat-box {
        width: 100%;                    /* Hücrenin tamamını kapsar */
        height: auto;
        font-size: 13px;
        padding: 10px;
        background-color: #1da1f2;
        color: white;
        border-radius: 10px;
        text-align: center;
        box-sizing: border-box;         /* İçerik dışarı taşmasın */
    }


    .instagram-stats .stat-box {
        width: 100%;
        height: auto;
        font-size: 13px;               /* Aynı font boyutu */
        padding: 10px;                 /* Twitter'da 10, Instagram'da 10px 12px idi => eşitledik */
        background-color: #1E1E1E;     /* İsterseniz #1da1f2 yapıp aynı renge getirebilirsiniz */
        color: #FFFFFF;
        border-radius: 10px;
        text-align: center;            /* Twitter'daki gibi ortalarsanız aynı görünür */
        box-sizing: border-box;
    }




    .stat-label {
        font-size: 14px;
        color: #ccc;
    }

    .stat-right {
        text-align: right;
    }

    .stat-value {
        font-size: 18px;
        font-weight: bold;
        color: #fff;
    }

    .instagram-stats .stat-label {
        font-size: 14px;
        color: #ccc;
        text-align: left;
    }
    .instagram-stats .stat-value {
        font-size: 16px;
        font-weight: bold;
        color: #fff;
        text-align: left;
    }



    .stat-change {
        font-size: 13px;
        margin-top: 2px;
    }



    .stat-change.up {
        color: #00ff88;
    }

    .stat-change.down {
        color: #ff5555;
    }
        :root {
            --bg-color: #121212;
            --card-bg: #1E1E1E;
            --text-primary: #FFFFFF;
            --text-secondary: #B3B3B3;
            --accent-blue: #2196F3;
            --border-color: #373737;
            --base-font-size: 14px;
            --section-header-size: 13px;
            --deck-padding: 12px;
            --gap-size: 10px;
            --title-bar-height: 36px;

            /* 1) Üst ve alt bar rengini biraz daha açtık (örnek: #3C3C3C) */
            --header-footer-bg: #3C3C3C;

            /* DPI duyarlı boyutlar için rem birimleri */
            --base-rem-size: 1rem;
            --small-rem-size: 0.875rem;
            --large-rem-size: 1.125rem;
        }
        html {
            /* Temel font boyutu - DPI ölçeklendirmesi için */
            font-size: 14px;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            background-color: var(--bg-color);
            font-family: 'Segoe UI', sans-serif;
            color: var(--text-primary);
            overflow: hidden;
            /* Rem tabanlı font boyutu */
            font-size: var(--base-rem-size);
        }

        .title-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--title-bar-height);
            background-color: var(--header-footer-bg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            z-index: 999;
            -webkit-user-select: none;
            cursor: default;
        }

        .status-indicator-bar {
            position: absolute;
            top: 9px; /* ¡¡¡ Aşağıya indirildi */
            left: 50%;
            transform: translateX(-50%);
            width: 100px;  /* ? Daha geniş */
            height: 20px;  /* ^ Daha yüksek */
            background-color: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
            z-index: 9999;
        }

        .status-light-bar {
            width: 100px;
            height: 20px;
            border-radius: 10px;
            box-shadow: 0 0 4px #00000055;
            background-size: 200% 100%;
        }



        @keyframes idleAnim {
            0% { background-position: 100% 0; }
            100% { background-position: -100% 0; }
        }

        @keyframes activeAnim {
            0% { background-position: 0% 50%; background-color: #2ecc71; }
            100% { background-position: 100% 50%; background-color: #27ae60; }
        }

        @keyframes stopAnim {
            0% { background-position: 0% 50%; background-color: #e74c3c; }
            100% { background-position: 100% 50%; background-color: #c0392b; }
        }

        /* YENİ EKLENEN - Akış animasyonları */
        @keyframes flowGreen {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        @keyframes flowRed {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        /* --- YENİ EKLENEN SONU --- */





        .window-title {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg,
                #FFD700 0%,
                #FFD700 45%,
                #FFFFFF 45%,
                #FFFFFF 55%,
                #FFD700 55%,
                #FFD700 100%
            );
            background-size: 200%;
            background-repeat: repeat;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: shine 3s infinite alternate;
        }



        @keyframes shine {
            0% {
                background-position: 0% 0;
                animation-timing-function: linear;
            }
            80% {
                background-position: 90% 0;
                animation-timing-function: ease-out;
            }
            100% {
                background-position: 100% 0;
            }
        }

        .window-buttons {
            display: flex;
            gap: 6px;
        }
        .window-button {
            width: 24px;
            height: 24px;
            border: none;
            border-radius: 4px;
            background: var(--border-color);
            color: var(--text-primary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            -webkit-user-select: none;
        }
        .window-button:hover {
            background: var(--accent-blue);
            color: #fff;
        }

        #close-btn:hover {
            background-color: #D32F2F !important;
            color: #fff !important;
        }


        .extra-buttons {
            display: flex;
            gap: 20px;
            margin-left: auto;
            margin-right: 40px;
        }
        .title-bar-button {
            padding: 5px 12px;
            background: #E0E0E0; /* pastel açık renk */
            border: none;
            border-radius: 4px;
            color: #333; /* koyu yazı */
            font-size: 12px;
            cursor: pointer;
        }
        #signup-btn {
            margin-right: 10px;
        }
        .title-bar-button:hover {
            background: #d5d5d5;
        }

        .grid-container {
            position: absolute;
            top: calc(var(--title-bar-height) + 10px);
            left: var(--gap-size);
            right: 70px; /* en sağdaki bar için boşluk */
            bottom: 60px; /* alt barda 60px yer var */
            display: grid;
            /* sol deck eski genişliğinde, orta deck genişletildi, en sağdaki can barları daraltıldı */
            grid-template-columns: 0.64fr 4px 1.2fr 4px 1fr 4px 0.8fr;
            grid-template-rows: 1fr;
            gap: var(--gap-size);
            /* Minimum genişlik ayarları - çok küçük ekranlarda düzen bozulmasını önler */
            min-width: 700px;
        }
        .resizer {
            background: var(--border-color);
            cursor: col-resize;
            width: 4px;
        }
        .resizer:hover {
            background: var(--accent-blue);
        }
        .deck {
            background: var(--card-bg);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: var(--deck-padding);
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) var(--bg-color);
        }
            .deck > * {
            transition: all 0.4s ease-in-out;
        }

        .deck::-webkit-scrollbar {
            width: 10px;
            background-color: var(--bg-color);
        }
        .deck::-webkit-scrollbar-track {
            background-color: var(--bg-color);
        }
        .deck::-webkit-scrollbar-thumb {
            background-color: var(--border-color);
            border-radius: 6px;
            border: 2px solid var(--bg-color);
        }
        .deck::-webkit-scrollbar-thumb:hover {
            background-color: var(--accent-blue);
        }
        .left-deck {
            display: flex;
            flex-direction: column;
            gap: var(--gap-size);
        }
        .profile-selector {
            margin-bottom: 12px;
        }
        .selector-buttons {
            margin-bottom: 12px;
        }
        .platform-select {
            width: 100%;
            padding: 8px;
            border: 1px solid #B0B0B0;
            border-radius: 5px;
            background-color: #F5F5F7;
            color: #000000;
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
        }
        .platform-select option {
            background-color: #F5F5F7;
            color: #000000;
            font-weight: bold;
        }
        .platform-select option:hover {
            background-color: #E0E0E5;
            color: #000000;
        }
        .profile-list {
            margin-top: 6px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .profile-item {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
            font-size: 13px;
            text-align: center;
        }

        .profile-item:hover {
            background: var(--accent-blue);
        }
        .middle-deck {}
        .tab-bar {
            display: flex;
            gap: 12px;
            margin-bottom: 10px;
        }
        .tab {
            color: var(--text-secondary);
            cursor: pointer;
            padding-bottom: 2px;
            font-weight: 500;
            font-size: 14px;
        }
        .tab.active {
            color: var(--text-primary);
            border-bottom: 2px solid var(--accent-blue);
        }
        .list-item {
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }
        .list-item:last-child {
            border-bottom: none;
        }
        .thread-count {
            background: var(--accent-blue);
            color: white;
            padding: 2px 4px;
            border-radius: 4px;
            font-size: 11px;
        }

        .right-deck {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        .section {
            margin-bottom: 14px;
        }
        .section-header {
            font-size: var(--section-header-size);
            color: var(--text-secondary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
            flex-wrap: nowrap;
            white-space: nowrap;
            overflow: hidden;
        }
        .badge {
            background: #373737;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        ul {
            list-style: none;
            padding-left: 0;
            margin: 6px 0;
        }
        ul li {
            position: relative;
            padding-left: 16px;
            margin-bottom: 4px;
            font-size: 14px;
        }
        ul li::before {
            content: "•";
            position: absolute;
            left: 0;
            color: var(--accent-blue);
        }

        .bottom-bar {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--header-footer-bg);
            border-top: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            height: 50px;
        }
        .bottom-buttons {
            display: flex;
            gap: 12px;
        }
        .action-button {
            background: #292A2D;
            border: 1px solid #3A3B3E;
            padding: 8px 14px;
            border-radius: 6px;
            color: #F8F8F8;
            font-size: 14px;
            font-family: 'Segoe UI', sans-serif;
            letter-spacing: 0.4px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s, color 0.3s, border-color 0.3s;
        }
        .action-button:hover {
            background: #35363A;
            color: #FFFFFF;
            border-color: #505255;
        }
        .action-button.primary {
            background: #006400;
            color: #EAEAEA;
            font-weight: bold;
            font-size: 15px;
            letter-spacing: 0.6px;
            border: 1px solid #163A5E;
        }
        .action-button.primary:hover {
            background: #008f5a; /* daha koyu, yumuşak bir yeşil */
            border-color: #267f60; /* daha yumuşak bir kontrast */
            color: #FFFFFF;
        }

        #stopBtn {
            background: #5E1E1E;
            border: 1px solid #4A1919;
            color: #FFFFFF;
        }
        #stopBtn:hover {
            background: #C0392B;
            border-color: #A93226;
            color: #FFFFFF;
        }

        .right-side-bar {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            width: 50px;
            background-color: var(--header-footer-bg);
            border-left: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 45px;
        }
        .right-side-bar-button {
            font-size: 20px;
            background: none;
            border: none;
            color: var(--text-primary);
            cursor: pointer;
            margin-bottom: 8px;
        }
        .right-side-bar-button:hover {
            color: var(--accent-blue);
        }

        /* -- İSTATİSTİK KUTUCUKLARI İÇİN YENİ CSS -- */
.stats-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.stats-title {
    font-size: 16px;
    font-weight: bold;
    margin: 0;
    color: #fff;
}

.stats-update-time {
    font-size: 12px;
    color: #aaa;
}

/* === GRID DÜZENİ === */
.stats-grid {
    display: grid;
    /* Genişliğe göre otomatik kolon sayısı (120 px'den küçükse tek sütuna iner) */
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.stat-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #1e1e1e;
    padding: 10px 15px;
    border-radius: 10px;
}


.stat-label {
    font-size: 13px;
    color: #aaa;
}

.stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
}

.stat-change {
    font-size: 12px;
    font-weight: bold;
}

/* Artış = yeşil */
.stat-change.up {
    color: #4caf50;
}

/* Düşüş = kırmızı */
.stat-change.down {
    color: #f44336;
}


        .extra-deck {
            background: var(--card-bg);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: var(--deck-padding);
            overflow-y: auto;
        }

        /* Live Feed Stilleri */
        .live-feed-card {
            display: flex;
            align-items: center;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 8px;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .live-feed-card:hover {
            border-color: var(--accent-blue);
            background: #252525;
        }

        .live-feed-logo {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .live-feed-username {
            font-weight: bold;
            color: var(--text-primary);
            font-size: 13px;
            min-width: 60px;
        }

        .live-feed-check {
            color: #4caf50;
            font-size: 16px;
            font-weight: bold;
            margin-left: auto;
        }

        .live-feed-progressbar {
            flex: 1;
            height: 6px;
            background: #333;
            border-radius: 3px;
            overflow: hidden;
            margin: 0 8px;
        }

        .live-feed-bar {
            height: 100%;
            background: linear-gradient(90deg, #2196F3, #21CBF3);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .live-feed-counter {
            font-size: 11px;
            color: var(--text-secondary);
            background: #333;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 30px;
            text-align: center;
        }

        .live-feed-desc {
            flex: 1;
            font-size: 12px;
            color: var(--text-secondary);
            margin: 0 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .live-feed-thumb {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            object-fit: cover;
            flex-shrink: 0;
        }

        .live-feed-skeleton {
            background: linear-gradient(-90deg, #2b2b2b 0%, #3a3a3a 50%, #2b2b2b 100%);
            background-size: 400% 400%;
            animation: shimmer 1.2s ease-in-out infinite;
            border-radius: 8px;
            height: 60px;
            width: 100%;
            margin-bottom: 8px;
        }

        .live-feed-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
            transition: all 0.3s ease;
        }

        /* Inline Profile Editor Styles */
        #live-feed-container.profile-editor-mode {
            position: relative;
            padding: 20px;
            display: flex;
            flex-direction: column;
            height: 100%;
            box-sizing: border-box;
        }

        #live-feed-container button {
            transition: all 0.2s ease;
        }

        #live-feed-container input {
            transition: border-color 0.2s ease, background-color 0.2s ease;
        }

        #live-feed-container input:focus {
            border-color: #2196F3;
            background-color: #252525;
            outline: none;
        }

        .live-feed-skeleton-card {
            display: flex; flex-direction: column; gap: 8px; align-items: flex-start; background: #232323; border-radius: 10px; padding: 13px 16px; margin-bottom: 13px; width: 100%; box-sizing: border-box;
        }
        .live-feed-skeleton-logo {
            width: 36px; height: 36px; border-radius: 8px; margin-bottom: 3px; background: #313131;
        }
        .live-feed-skeleton-user {
            width: 105px; height: 17px; border-radius: 5px; background: #282828;
        }
        .live-feed-skeleton-bar {
            width: 94%; height: 14px; border-radius: 7px; background: linear-gradient(-90deg, #2b2b2b 0%, #3a3a3a 50%, #2b2b2b 100%); background-size: 400% 400%; animation: shimmer 1.2s ease-in-out infinite;
        }
        .live-feed-event-card {
            background: #232323; border-radius: 10px; margin-bottom: 13px;
            box-shadow: 0 1px 4px #0002; padding: 14px 16px; display: flex; flex-direction: column;
            width: 100%; max-width: 100%; box-sizing: border-box;
        }
        .stat-header {
            display: flex; align-items: center; gap: 6px; margin-bottom: 8px;
            flex-wrap: nowrap;
            white-space: nowrap;
            overflow: hidden;
        }
        .stat-logo {
            width: 36px; height: 36px; border-radius: 8px; object-fit: contain; background: #292929; margin-right: 4px;
        }
        .stat-username {
            font-size: 14px; font-weight: 600; color: #fff; letter-spacing: .2px; font-family: 'Segoe UI', Arial, sans-serif;
            margin-right: 8px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .stat-counter {
            font-size: 13px; color: #a9ebc5; margin-left: auto;
        }
        .stat-checkmark {
            color: #45ff5e; font-size: 14px; margin-left: 6px; font-weight: bold;
            animation: stat-tick-pop .6s cubic-bezier(.4,1.6,.6,1) 1;
        }
        @keyframes stat-tick-pop {
            0% { transform: scale(0.2); opacity: 0; }
            55% { transform: scale(1.17); opacity: 1; }
            100% { transform: scale(1); }
        }
        .stat-progress-container {
            width: 97%; height: 14px; background: #171c17; border-radius: 8px;
            margin-top: 4px; margin-bottom: 1px; overflow: hidden;
        }
        .stat-progress-bar {
            height: 14px; background: linear-gradient(90deg,#2bc759,#baffc7);
            border-radius: 8px; transition: width 0.5s cubic-bezier(.39,1.4,.6,1);
        }
        .stat-progress-bar.animated {
            animation: progressAnimation 3s ease-in-out forwards;
        }
        @keyframes progressAnimation {
            0% { width: 0%; }
            100% { width: 100%; }
        }
        .stat-post-desc {
            font-size: 12px; color: #f6f6f6; background: #232e23; border-radius: 5px;
            padding: 6px 10px; margin: 4px 0 8px 0; font-family: 'Segoe UI', Arial, sans-serif;
            font-weight: 500; letter-spacing: .1px; line-height: 1.3;
            display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical;
            overflow: hidden; text-overflow: ellipsis; word-wrap: break-word;
        }
        .stat-post-thumb {
            width: 99%; max-width: 340px; height: 92px; border-radius: 7px;
            object-fit: cover; background: #222; box-shadow: 0 0 9px #0005;
            margin: 0 0 2px 0; position: relative;
        }
        .stat-post-thumb-container {
            position: relative; display: inline-block; width: 100%;
        }
        .stat-video-icon {
            position: absolute; bottom: 6px; left: 6px; width: 20px; height: 20px;
            background: rgba(0,0,0,0.7); border-radius: 3px; display: flex;
            align-items: center; justify-content: center; color: #fff; font-size: 12px;
        }
        .stat-status-text {
            font-size: 13px; color: #f6f6f6; background: #232e23; border-radius: 5px;
            padding: 6px 10px; margin: 0 0 8px 0; font-family: 'Segoe UI', Arial, sans-serif;
            font-weight: 500; letter-spacing: .1px; text-align: center;
            display: flex; align-items: center; justify-content: center; gap: 6px;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="title-bar">
        <div class="window-title">sorcerio</div>

    <div class="status-indicator-bar">
      <div class="status-light-bar" id="statusLight" style="animation: stopAnim 1.5s ease-in-out infinite;"></div>
    </div>

        <div class="window-buttons">
            <button class="window-button" id="min-btn" title="Simge Durumuna Küçült">–</button>
            <button class="window-button" id="max-btn" title="Büyüt">&#9744;</button> <!-- Unicode: ? -->
            <button class="window-button" id="close-btn" title="Kapat">&#10005;</button> <!-- Unicode: ? -->
        </div>
    </div>

    <div class="grid-container">
    <div class="deck left-deck">
        <div class="profile-selector">
            <div class="selector-buttons">
                <select id="platformSelect" class="platform-select">
                    <option value="instagram">Instagram</option>
                    <option value="twitter">Twitter</option>
                </select>
            </div>
            <div id="profile-list" class="profile-list"></div>
        </div>
    </div>

    <script>

    </script>


        <div class="resizer" id="resizer-1"></div>

        <div class="deck middle-deck">
    <div class="live-feed-container" id="live-feed-container">
        <div class="skeleton-container" id="middle-skeleton">
            <div class="skeleton" style="width: 80%; height: 24px;"></div>
            <div class="skeleton" style="width: 60%; height: 20px;"></div>
            <div class="skeleton" style="width: 90%; height: 24px;"></div>
            <div class="skeleton" style="width: 70%; height: 18px;"></div>
        </div>
    </div>
</div>



        <div class="resizer" id="resizer-2"></div>

        <div class="deck right-deck">
        <div class="skeleton-container" id="right-skeleton">
    <div class="skeleton" style="width: 85%; height: 45px;"></div>
    <div class="skeleton" style="width: 80%; height: 45px;"></div>
    <div class="skeleton" style="width: 75%; height: 45px;"></div>
</div>


    <!-- Twitter istatistik kutuları -->
    <div class="stats-grid twitter-stats">
        <!-- Twitter kutuları burada oluşturulacak -->
    </div>

    <!-- Instagram istatistik kutuları -->
    <div class="stats-grid instagram-stats">
        <!-- Instagram kutuları burada oluşturulacak -->
    </div>
</div>


        <div class="resizer" id="resizer-3"></div>

        <div class="deck extra-deck">
    <div class="skeleton-container" id="extra-skeleton">
        <div class="skeleton" style="height: 20px; width: 70%;"></div>
        <div class="skeleton" style="height: 14px; width: 100%;"></div>
        <div class="skeleton" style="height: 14px; width: 90%;"></div>
        <div class="skeleton" style="height: 14px; width: 80%;"></div>
    </div>
</div>

    </div>

    <!-- Orijinal bottom-bar bloğunu bu güncellenmiş haliyle değiştirin -->
    <div class="bottom-bar">
      <div class="bottom-buttons">
        <button class="action-button primary" id="startBtn">Başla</button>
        <button class="action-button"        id="stopBtn">Durdur</button>
        <button class="action-button"        id="errorBtn">DESTEK</button>
      </div>
    </div>



    <div class="right-side-bar">
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            new QWebChannel(qt.webChannelTransport, function(channel) {
                let pyBridge = channel.objects.pyBridge;
                let profileBridge = channel.objects.profileBridge;

                function setStatusBarColor(colorState) {
                    const bar = document.querySelector(".status-light-bar");
                    if (!bar) return;

                    // Mevcut animasyonları sıfırla
                    bar.style.animation = 'none';
                    bar.style.backgroundImage = 'none';

                    let gradient = '';
                    let animationName = '';
                    let duration = '1.8s';

                    if (colorState === "working") {
                        // Daha keskin geçişli yeşil akış
                        gradient = 'linear-gradient(90deg, #1e8e3e 0%, #1e8e3e 40%, #34c759 50%, #1e8e3e 60%, #1e8e3e 100%)';
                        animationName = 'flowGreen';
                    } else if (colorState === "stopped") {
                        // Daha keskin geçişli kırmızı akış
                        gradient = 'linear-gradient(90deg, #a02c2c 0%, #a02c2c 40%, #e74c3c 50%, #a02c2c 60%, #a02c2c 100%)';
                        animationName = 'flowRed';
                    } else {
                        // Gri idle akışı
                        gradient = 'linear-gradient(90deg, #444 0%, #444 40%, #666 50%, #444 60%, #444 100%)';
                        duration = '2.5s';
                    }

                    bar.style.backgroundImage = gradient;
                    if (animationName) {
                        bar.style.animation = `${animationName} ${duration} linear infinite`;
                    }

                    // Animasyonu tetikle
                    void bar.offsetWidth;
                }




                // Sayfa yüklendiğinde başlangıçta kırmızı göster
                setStatusBarColor("stopped");



                // Pencere kontrol butonları
                document.getElementById("min-btn").addEventListener("click", function() {
                    pyBridge.minimizeWindow();
                });
                document.getElementById("max-btn").addEventListener("click", function() {
                    pyBridge.maximizeWindow();
                });
                document.getElementById("close-btn").addEventListener("click", function() {
                    pyBridge.closeWindow();
                });
                document.getElementById("startBtn").addEventListener("click", function() {
                  pyBridge.startProcessingFromJS();
                  setStatusBarColor("working");
                });

                document.getElementById("stopBtn").addEventListener("click", function() {
                  pyBridge.stopProcessing();
                  setStatusBarColor("stopped");
                });






                // Başlık çubuğu sürükleme
                let titleBar = document.querySelector(".title-bar");
                let isDraggingWindow = false;
                titleBar.addEventListener("mousedown", function(e) {
                    if (e.target.closest('.window-buttons') || e.target.closest('.extra-buttons')) {
                        return;
                    }
                    isDraggingWindow = true;
                    pyBridge.startDrag();
                });
                document.addEventListener("mouseup", function(e) {
                    if (isDraggingWindow) {
                        isDraggingWindow = false;
                        pyBridge.stopDrag();
                    }
                });
                document.addEventListener("mousemove", function(e) {
                    if (isDraggingWindow) {
                        pyBridge.dragWindow();
                    }
                });

                // Resizerlar
                const resizer1 = document.getElementById('resizer-1');
                const resizer2 = document.getElementById('resizer-2');
                const resizer3 = document.getElementById('resizer-3');
                const gridContainer = document.querySelector('.grid-container');
                let isResizing = false;
                let currentResizer = null;
                let startX = 0;
                let leftColWidth = 0;
                let middleColWidth = 0;
                let rightColWidth = 0;
                let extraColWidth = 0;

                function getPxWidths() {
                    const leftDeck = document.querySelector('.left-deck');
                    const middleDeck = document.querySelector('.middle-deck');
                    const rightDeck = document.querySelector('.right-deck');
                    const extraDeck = document.querySelector('.extra-deck');
                    let leftRect = leftDeck.getBoundingClientRect();
                    let midRect = middleDeck.getBoundingClientRect();
                    let rightRect = rightDeck.getBoundingClientRect();
                    let extraRect = extraDeck.getBoundingClientRect();
                    leftColWidth = leftRect.width;
                    middleColWidth = midRect.width;
                    rightColWidth = rightRect.width;
                    extraColWidth = extraRect.width;
                }
                function setGridColumns(l, m, r, e) {
                    gridContainer.style.gridTemplateColumns = l + 'px 4px ' + m + 'px 4px ' + r + 'px 4px ' + e + 'px';
                }
                function mouseDownHandler(e, whichResizer) {
                    isResizing = true;
                    currentResizer = whichResizer;
                    startX = e.clientX;
                    getPxWidths();
                    document.addEventListener('mousemove', mouseMoveHandler);
                    document.addEventListener('mouseup', mouseUpHandler);
                    e.preventDefault();
                }
                function mouseMoveHandler(e) {
                    if (!isResizing) return;
                    let dx = e.clientX - startX;
                    let minW = 100;
                    if (currentResizer === resizer1) {
                        let newLeft = leftColWidth + dx;
                        let newMid = middleColWidth - dx;
                        if (newLeft < minW) {
                            newLeft = minW;
                            newMid = leftColWidth + middleColWidth - minW;
                        }
                        if (newMid < minW) {
                            newMid = minW;
                            newLeft = leftColWidth + middleColWidth - minW;
                        }
                        setGridColumns(newLeft, newMid, rightColWidth, extraColWidth);
                    } else if (currentResizer === resizer2) {
                        let newMid = middleColWidth + dx;
                        let newRight = rightColWidth - dx;
                        if (newMid < minW) {
                            newMid = minW;
                            newRight = middleColWidth + rightColWidth - minW;
                        }
                        if (newRight < minW) {
                            newRight = minW;
                            newMid = middleColWidth + rightColWidth - minW;
                        }
                        setGridColumns(leftColWidth, newMid, newRight, extraColWidth);
                    } else if (currentResizer === resizer3) {
                        let newRight = rightColWidth + dx;
                        let newExtra = extraColWidth - dx;
                        if (newRight < minW) {
                            newRight = minW;
                            newExtra = rightColWidth + extraColWidth - minW;
                        }
                        if (newExtra < minW) {
                            newExtra = minW;
                            newRight = rightColWidth + extraColWidth - minW;
                        }
                        setGridColumns(leftColWidth, middleColWidth, newRight, newExtra);
                    }
                }
                function mouseUpHandler() {
                    isResizing = false;
                    currentResizer = null;
                    document.removeEventListener('mousemove', mouseMoveHandler);
                    document.removeEventListener('mouseup', mouseUpHandler);
                }
                resizer1.addEventListener('mousedown', (e) => mouseDownHandler(e, resizer1));
                resizer2.addEventListener('mousedown', (e) => mouseDownHandler(e, resizer2));
                resizer3.addEventListener('mousedown', (e) => mouseDownHandler(e, resizer3));

                // Platform seçimi (dropdown)
                const platformSelect = document.getElementById("platformSelect");
                const profileListDiv = document.getElementById("profile-list");

                window.profileItems = [];

                function updateProfileList(response) {
                    // 1) JSON yanıtını işle
                    let profiles = JSON.parse(response);
                    window.profileItems = profiles;

                    // 2) Eski profil öğelerini temizle
                    const profileListDiv = document.getElementById("profile-list");
                    profileListDiv.innerHTML = "";

                    // 3) Yeni profilleri ekle
                    profiles.forEach(function(p) {
                        let div = document.createElement("div");
                        div.className = "profile-item";
                        div.textContent = p.displayName;
                        div.setAttribute("data-profile-path", p.path);
                        div.addEventListener("click", function() {
                            profileBridge.showProfileEditorInline(p.path);
                        });
                        profileListDiv.appendChild(div);
                    });
                }


                window.updateProfileItemText = function(path, newName) {
                    // 1) İç veriyi güncelle
                    window.profileItems.forEach(item => {
                        if (item.path === path) {
                            item.displayName = newName;
                        }
                    });
                    // 2) DOM'daki öğeyi güncelle
                    document.querySelectorAll('.profile-item').forEach(el => {
                        if (el.getAttribute('data-profile-path') === path) {
                            el.textContent = newName;
                        }
                    });
                };

                // Profil listesini yenileme fonksiyonu - Python'daki sync fonksiyonu ile bağlantılı
                window.refreshProfilesList = function() {
                    if (document.getElementById("platformSelect")) {
                        let selected = document.getElementById("platformSelect").value;
                        // Önce profil listesini gösteren iskelet
                        const profileListDiv = document.getElementById("profile-list");
                        if (profileListDiv) {
                            profileListDiv.innerHTML = '<div class="skeleton" style="width: 90%; height: 32px;"></div><div class="skeleton" style="width: 80%; height: 32px;"></div><div class="skeleton" style="width: 95%; height: 32px;"></div>';
                        }
                        // Profil listesini yenile
                        profileBridge.getProfiles(selected, function(response) {
                            updateProfileList(response);
                        });
                    }
                };

                platformSelect.addEventListener("change", function() {
                    let selected = platformSelect.value;
                    // Platform değiştiğinde önce profil-JSON senkronizasyonunu çağır
                    // Bu yapı JS -> Python -> JS şeklinde bir döngü oluşturur
                    pyBridge.syncProfilesWithJSON = function() {
                        // sync_profiles_with_json Python fonksiyonu yine JS refreshProfilesList fonksiyonunu çağıracak
                        if (typeof window.mainWindow !== 'undefined' && window.mainWindow.sync_profiles_with_json) {
                            window.mainWindow.sync_profiles_with_json();
                        }
                    };
                    profileBridge.getProfiles(selected, function(response) {
                        updateProfileList(response);
                    });
                });

                // Sayfa yüklenince varsayılan "instagram" profilleri göster
                platformSelect.value = "instagram";
                profileBridge.getProfiles("instagram", function(response) {
                    updateProfileList(response);
                });

                // --------------------------------------------------
                // DESTEK BUTONU TIKLANDIĞINDA PYTHONDAKİ FONKSİYON ÇAĞRILACAK
                // --------------------------------------------------
                document.getElementById("errorBtn").addEventListener("click", function() {
                    pyBridge.openDestekWindow();
                });

                // --------------------------------------------------
                // INLINE PROFILE EDITOR FUNCTIONS
                // --------------------------------------------------
                window.setupInlineProfileEditor = function(profilePath) {
                    // Close button handlers
                    const closeBtn = document.getElementById("inline-close-btn");
                    const closeBtn2 = document.getElementById("inline-close-btn-2");

                    if (closeBtn) {
                        closeBtn.addEventListener("click", function() {
                            profileBridge.hideProfileEditorInline();
                        });
                    }

                    if (closeBtn2) {
                        closeBtn2.addEventListener("click", function() {
                            profileBridge.hideProfileEditorInline();
                        });
                    }

                    // Save button handler
                    const saveBtn = document.getElementById("inline-save-btn");
                    if (saveBtn) {
                        saveBtn.addEventListener("click", function() {
                            const username = document.getElementById("inline-username").value;
                            const password = document.getElementById("inline-password").value;
                            const hashtags = document.getElementById("inline-hashtags").value;

                            profileBridge.saveProfileInline(profilePath, username, password, hashtags);
                        });
                    }

                    // Username change handler for profile name update - match old implementation exactly
                    const usernameInput = document.getElementById("inline-username");
                    if (usernameInput) {
                        usernameInput.addEventListener("input", function() {
                            const newUsername = this.value.trim();
                            const profileNameDiv = document.getElementById("inline-profile-name");

                            if (profileNameDiv) {
                                if (newUsername) {
                                    profileNameDiv.textContent = newUsername;
                                    profileNameDiv.style.cssText = `
                                        color: black;
                                        background: linear-gradient(135deg, #ffe082 0%, #fff9c4 50%, #ffd54f 100%);
                                        border: 1px solid #d6a300;
                                        border-radius: 6px;
                                        padding: 6px 12px;
                                        font-family: 'Segoe UI', 'Arial', sans-serif;
                                        font-size: 16px;
                                        font-weight: bold;
                                        letter-spacing: 0.2px;
                                        display: inline-block;
                                    `;
                                } else {
                                    // Use single space character for fixed width display like old implementation
                                    profileNameDiv.textContent = " ";
                                    profileNameDiv.style.cssText = `
                                        background: transparent;
                                        border: 1px solid transparent;
                                        border-radius: 6px;
                                        color: #888;
                                        padding: 6px 12px;
                                        font-family: 'Segoe UI', 'Arial', sans-serif;
                                        font-size: 16px;
                                        font-weight: bold;
                                        letter-spacing: 0.2px;
                                        display: inline-block;
                                    `;
                                }
                            }
                        });
                    }

                    // Time Settings button handler
                    const timeSettingsBtn = document.getElementById("inline-time-settings-btn");
                    if (timeSettingsBtn) {
                        timeSettingsBtn.addEventListener("click", function() {
                            // Show time settings dialog as popup
                            profileBridge.showTimeSettingsInStats(profilePath);
                        });
                    }

                    // Link Add button handler
                    const linkAddBtn = document.getElementById("inline-link-add-btn");
                    if (linkAddBtn) {
                        linkAddBtn.addEventListener("click", function() {
                            // Show link editor dialog as popup
                            profileBridge.showLinkEditorInStats(profilePath);
                        });
                    }

                    // Add hover effects for buttons
                    const buttons = document.querySelectorAll('#live-feed-container button');
                    buttons.forEach(button => {
                        button.addEventListener('mouseenter', function() {
                            if (this.id !== 'inline-close-btn') {
                                this.style.backgroundColor = '#2196F3';
                            } else {
                                this.style.backgroundColor = '#FF4444';
                            }
                        });

                        button.addEventListener('mouseleave', function() {
                            if (this.id !== 'inline-close-btn') {
                                this.style.backgroundColor = '#242424';
                            } else {
                                this.style.backgroundColor = '#D32F2F';
                            }
                        });
                    });
                };






            });
        });
    </script>
</body>
</html>
"""
dummy_module = types.ModuleType("moviepy.editor")


class DummyVideoFileClip:
    def __init__(self, path):
        self.path = path
        self.duration = dummy_duration(path)
        try:
            cap = cv2.VideoCapture(self.path)
            if cap.isOpened():
                width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
                height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
                self.size = (int(width), int(height))
            else:
                self.size = (640, 480)
            cap.release()
        except Exception:
            self.size = (640, 480)

    def save_frame(self, filename, t=0, withmask=True, **kwargs):
        cap = cv2.VideoCapture(self.path)
        if not cap.isOpened():
            cap.release()
            raise Exception("Video açılamadı.")
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps == 0:
            cap.release()
            raise Exception("FPS değeri 0.")
        frame_index = int(t * fps)
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_index)
        ret, frame = cap.read()
        if ret:
            cv2.imwrite(filename, frame)
        else:
            raise Exception("Belirtilen zamanda kare yakalanamadı.")
        cap.release()

    def close(self):
        pass


def setup_configurations():
    """
    Ana dizinde configuration adlı klasör oluşturur.
    Bu klasör altında twitter ve instagram adında iki alt klasör oluşturulur.
    Her biri için 8 adet profile json dosyası oluşturulur.
    Her json dosyasında:
      - username
      - password
      - schedule (gün bazlı -> liste, her güne istediğiniz sayıda saat)
      - hashtags (liste)
      - links (tek bir liste, gün bazlı değil: eklenecek video linkleri)
    Ayrıca, Instagram için oturum dosyalarının bulunduğu `sessions` klasöründe
    JSON içinde artık tanımlı olmayan kullanıcı adlarına ait session dosyaları kontrol edilir:
    - Eğer session dosyası 24 saatten eskiyse silinir
    - 24 saatten taze ise, profil silinmiş olsa bile tekrar kullanılabilmesi için saklanır
    """
    base_dir = os.path.dirname(os.path.abspath(__file__))
    config_dir = os.path.join(base_dir, "configuration")
    twitter_config_dir = os.path.join(config_dir, "twitter")
    instagram_config_dir = os.path.join(config_dir, "instagram")

    os.makedirs(twitter_config_dir, exist_ok=True)
    os.makedirs(instagram_config_dir, exist_ok=True)

    default_schedule = {
        "monday": [],
        "tuesday": [],
        "wednesday": [],
        "thursday": [],
        "friday": [],
        "saturday": [],
        "sunday": [],
    }

    default_links = []

    default_config = {
        "username": "",
        "password": "",
        "schedule": default_schedule,
        "hashtags": [],
        "links": default_links,
        "downloaded": [],
    }

    # Twitter profilleri
    for i in range(1, 9):
        config_file = os.path.join(twitter_config_dir, f"profile{i}.json")
        if not os.path.exists(config_file):
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(default_config, f, indent=4)
        else:
            try:
                with open(config_file, "r", encoding="utf-8") as f:
                    existing = json.load(f)
                if (not existing.get("username")
                        and not existing.get("password")
                        and not existing.get("links")):
                    logging.info(f"{config_file} boş olduğu için profil yeniden oluşturulmadı.")
            except Exception as e:
                logging.warning(f"{config_file} kontrol edilirken hata: {e}")

    # Instagram profilleri
    for i in range(1, 9):
        config_file = os.path.join(instagram_config_dir, f"profile{i}.json")
        if not os.path.exists(config_file):
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(default_config, f, indent=4)
        else:
            try:
                with open(config_file, "r", encoding="utf-8") as f:
                    existing = json.load(f)
                if (not existing.get("username")
                        and not existing.get("password")
                        and not existing.get("links")):
                    logging.info(f"{config_file} boş olduğu için profil yeniden oluşturulmadı.")
            except Exception as e:
                logging.warning(f"{config_file} kontrol edilirken hata: {e}")

    logging.info("Configuration dosyaları oluşturuldu veya mevcut.")

    # --- Instagram session dosyalarını kontrol et ---
    session_dir = os.path.join(instagram_config_dir, "sessions")
    os.makedirs(session_dir, exist_ok=True)

    # 1) JSON içindeki aktif kullanıcı adlarını topla
    mevcut_userlar = set()
    for profile_file in os.listdir(instagram_config_dir):
        if not profile_file.endswith(".json"):
            continue
        profile_path = os.path.join(instagram_config_dir, profile_file)
        try:
            with open(profile_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            username = data.get("username", "").strip()
            if username:
                mevcut_userlar.add(username)
        except Exception as e:
            logging.warning(f"Profil JSON okunurken hata ({profile_file}): {e}")

    # 2) Session klasöründeki her "<username>_session.json" için doğrula
    now = datetime.now()
    for sf in os.listdir(session_dir):
        if sf.endswith("_session.json"):
            uname = sf[:-len("_session.json")]
            session_file_path = os.path.join(session_dir, sf)
            meta_file_path = os.path.join(session_dir, f"{uname}_session.meta.json")

            # Profil artık mevcut değilse, session dosyasının yaşını kontrol et
            if uname not in mevcut_userlar:
                # Meta dosyasından oluşturulma zamanını kontrol et
                session_age_hours = 25  # Varsayılan olarak 24 saatten eski kabul et

                try:
                    if os.path.exists(meta_file_path):
                        with open(meta_file_path, "r", encoding="utf-8") as mf:
                            meta = json.load(mf)
                            created_str = meta.get("created")
                            if created_str:
                                created = datetime.strptime(created_str, "%Y-%m-%d %H:%M:%S")
                                session_age_hours = (now - created).total_seconds() / 3600
                    else:
                        # Meta dosyası yoksa, dosya oluşturulma zamanını kullan
                        created = datetime.fromtimestamp(os.path.getctime(session_file_path))
                        session_age_hours = (now - created).total_seconds() / 3600
                except Exception as e:
                    logging.error(f"Session yaşı kontrol edilirken hata: {sf} - {str(e)}")

                # 24 saatten eski ise sil, değilse sakla
                if session_age_hours >= 24:
                    try:
                        os.remove(session_file_path)
                        logging.info(f"24 saatten eski session dosyası silindi: {sf}")
                        if os.path.exists(meta_file_path):
                            os.remove(meta_file_path)
                    except Exception as e:
                        logging.error(f"Session dosyası silinirken hata ({sf}): {e}")
                else:
                    logging.info(f"Session dosyası 24 saatten taze, saklanıyor: {sf} ({session_age_hours:.1f} saat)")

    # --- Twitter chrome_profile klasörlerini kontrol et ---
    # Twitter için de benzer bir kontrol yapalım
    twitter_session_dir = os.path.join(twitter_config_dir, "sessions")
    os.makedirs(twitter_session_dir, exist_ok=True)

    # Twitter profil klasörlerini kontrol et
    twitter_profile_dirs = [d for d in os.listdir(base_dir) if d.startswith("chrome_profile_")]

    # Twitter için aktif kullanıcı adlarını topla
    twitter_mevcut_userlar = set()
    for profile_file in os.listdir(twitter_config_dir):
        if not profile_file.endswith(".json"):
            continue
        profile_path = os.path.join(twitter_config_dir, profile_file)
        try:
            with open(profile_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            username = data.get("username", "").strip()
            if username:
                twitter_mevcut_userlar.add(username)
        except Exception as e:
            logging.warning(f"Twitter profil JSON okunurken hata ({profile_file}): {e}")

    # Twitter profil klasörlerini kontrol et
    for profile_dir in twitter_profile_dirs:
        username = profile_dir.replace("chrome_profile_", "")
        profile_path = os.path.join(base_dir, profile_dir)

        # Kullanıcı hala aktif mi?
        if username not in twitter_mevcut_userlar:
            # Session meta dosyasını kontrol et
            meta_file = os.path.join(twitter_session_dir, f"{username}_session.meta.json")
            session_age_hours = 25  # Varsayılan olarak 24 saatten eski kabul et

            if os.path.exists(meta_file):
                try:
                    with open(meta_file, "r", encoding="utf-8") as f:
                        meta = json.load(f)
                    created_str = meta.get("created")
                    if created_str:
                        created = datetime.strptime(created_str, "%Y-%m-%d %H:%M:%S")
                        session_age_hours = (now - created).total_seconds() / 3600
                except Exception as e:
                    logging.error(f"Twitter meta dosyası okunurken hata: {meta_file} - {e}")
            else:
                # Meta dosyası yoksa, klasör oluşturulma zamanını kullan
                try:
                    created = datetime.fromtimestamp(os.path.getctime(profile_path))
                    session_age_hours = (now - created).total_seconds() / 3600

                    # Meta dosyası oluştur
                    meta = {
                        "created": created.strftime("%Y-%m-%d %H:%M:%S"),
                        "last_used": now.strftime("%Y-%m-%d %H:%M:%S"),
                        "next_refresh": ""
                    }
                    with open(meta_file, "w", encoding="utf-8") as f:
                        json.dump(meta, f, indent=4, ensure_ascii=False)
                except Exception as e:
                    logging.error(f"Twitter profil klasörü yaşı kontrol edilirken hata: {profile_path} - {e}")

            # 24 saatten eski ise sil, değilse sakla
            if session_age_hours >= 24:
                try:
                    import shutil
                    shutil.rmtree(profile_path)
                    logging.info(f"24 saatten eski Twitter profil klasörü silindi: {profile_path}")

                    # Meta dosyasını da sil
                    if os.path.exists(meta_file):
                        os.remove(meta_file)
                except Exception as e:
                    logging.error(f"Twitter profil klasörü silinirken hata: {profile_path} - {e}")
            else:
                logging.info(f"Twitter profil klasörü 24 saatten taze, saklanıyor: {profile_path} ({session_age_hours:.1f} saat)")

    return config_dir


# Live Feed Fonksiyonları
def get_logo_path(platform):
    """Platform logosunun base64 encoded data URL'ini döndürür (istatistik deck'i ile aynı yöntem)"""
    import base64
    root = os.path.dirname(os.path.abspath(__file__))
    logo_file = f"{platform}.png"
    if platform == "twitter":
        logo_file = "x.png"  # Twitter uses x.png file

    logo_path = os.path.join(root, logo_file)

    # Convert to base64 data URL like statistics deck does
    try:
        with open(logo_path, "rb") as img_f:
            b64_logo = base64.b64encode(img_f.read()).decode()
        return f"data:image/png;base64,{b64_logo}"
    except Exception as e:
        logging.warning(f"Logo yüklenemedi: {logo_file} - {str(e)}")
        return ""


def format_live_feed_event(event):
    if event["type"] in ["download_start", "download_progress", "download_complete"]:
        # --- STATUS TEXT ---
        status = event.get("status", "started")
        if status == "complete":
            status_text = "İçerik İndirildi"
            check = '<span class="stat-checkmark">&#10004;</span>'
        else:
            status_text = "İçerik İndiriliyor..."
            check = ''

        # --- LOGO KISMI ---
        logo_html = f'<img src="{event["logo_path"]}" class="stat-logo" />' if event.get("logo_path") else ''
        user_html = f'<span class="stat-username">{event["username"]}</span>'

        # --- STATUS TEXT WITH CHECKMARK ---
        status_html = f'<div class="stat-status-text">{status_text}{check}</div>'

        # --- PROGRESS BAR KISMI ---
        percent = int(event.get("progress", 0))
        # Counter varsa sağa ekle (örn. 1/2)
        counter = f'<span class="stat-counter">{event.get("current", 1)} / {event.get("total", 1)}</span>' if event.get("total", 1) > 1 else ""

        # Animated progress bar for new downloads
        bar_class = "stat-progress-bar animated" if event.get("status") == "started" else "stat-progress-bar"
        bar_html = f'''
            <div class="stat-progress-container">
                <div class="{bar_class}" style="width:{percent}%;"></div>
            </div>
        '''
        return f'''
        <div class="live-feed-event-card">
            {status_html}
            <div class="stat-header">{logo_html}{user_html}{counter}</div>
            {bar_html}
        </div>
        '''
    elif event["type"] == "upload_post":
        logo_html = f'<img src="{event["logo_path"]}" class="stat-logo" />' if event.get("logo_path") else ''
        user_html = f'<span class="stat-username">{event["username"]}</span>'

        # Truncate description if longer than 100 characters
        desc = event.get("desc", "")
        if len(desc) > 100:
            desc = desc[:100] + "..."
        desc_html = f'<div class="stat-post-desc">{desc}</div>'

        # Handle thumbnail with preserved copy and video icon
        thumb_path = event.get("thumb", "")
        if thumb_path and os.path.exists(thumb_path):
            try:
                import base64, mimetypes
                mime_type, _ = mimetypes.guess_type(thumb_path)
                with open(thumb_path, 'rb') as img_f:
                    b64_thumb = base64.b64encode(img_f.read()).decode()
                thumb_url = f"data:{mime_type or 'image/jpeg'};base64,{b64_thumb}"
            except Exception:
                # Fallback – eski yöntem
                thumb_url = f"file:///{thumb_path.replace(os.sep, '/')}"

            # Check if this is a video post by looking for video file extensions or is_video flag
            is_video = event.get("is_video", False) or any(ext in thumb_path.lower() for ext in ['.mp4', '.mov', '.avi', '.mkv'])

            if is_video:
                thumb_html = f'''
                <div class="stat-post-thumb-container">
                    <img src="{thumb_url}" class="stat-post-thumb"/>
                    <div class="stat-video-icon">▶</div>
                </div>
                '''
            else:
                thumb_html = f'<img src="{thumb_url}" class="stat-post-thumb"/>'
        else:
            thumb_html = ''

        return f'''
        <div class="live-feed-event-card">
            <div class="stat-header">{logo_html}{user_html}</div>
            {desc_html}
            {thumb_html}
        </div>
        '''
    return ""


def get_skeleton_html():
    # Eski basit skeleton animasyonu - sadece yatay çubuklar
    return '''
    <div class="skeleton" style="width: 80%; height: 24px; margin: 6px auto;"></div>
    <div class="skeleton" style="width: 60%; height: 20px; margin: 6px auto;"></div>
    <div class="skeleton" style="width: 90%; height: 24px; margin: 6px auto;"></div>
    <div class="skeleton" style="width: 70%; height: 18px; margin: 6px auto;"></div>
    '''

# =============================================================================
# SIGNALS MODULE (signals.py içeriği)
# =============================================================================

from PyQt5.QtCore import QObject, pyqtSignal

class FeedSignalEmitter(QObject):
    add_item_to_feed_signal = pyqtSignal(dict)
    update_feed_item_signal = pyqtSignal(dict)

feed_emitter = FeedSignalEmitter()

def update_feed_event(event_id, updated_event):
    """Update an existing feed event"""
    live_feed_manager.update_event(event_id, updated_event)

# =============================================================================
# SCHEDULER UTILS MODULE (scheduler_utils.py içeriği)
# =============================================================================

from apscheduler.schedulers.background import BackgroundScheduler

def reload_scheduler_helper(obj):
    """
    obj: scheduler attribute'u olan bir nesne (ör: MainWindow veya ilgili context)
    Scheduler zaten çalışıyorsa yeniden başlatmaz, sadece job listesini günceller.
    """
    if not hasattr(obj, "scheduler"):
        obj.scheduler = BackgroundScheduler(
            timezone="Europe/Istanbul",
            job_defaults={
                "misfire_grace_time": 300,
                "coalesce": True,
                "max_instances": 1
            }
        )
    else:
        obj.scheduler.remove_all_jobs()

    from upload import schedule_all_profiles_uploads
    schedule_all_profiles_uploads(obj.scheduler)

    # Sadece durmuşsa başlat
    if not getattr(obj.scheduler, "running", False):
        obj.scheduler.start()
        logging.info("Scheduler başlatıldı.")
    else:
        logging.info("Scheduler güncellendi (yeniden başlatılmadı).")

# =============================================================================
# CONFIG MODULE (config.py içeriği)
# =============================================================================

import platform

chromium_links = {
    "win64": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Win_x64%2F1110125%2Fchrome-win.zip?generation=1677456389285817&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Win_x64%2F1110125%2Fchromedriver_win32.zip?generation=1677456585621401&alt=media"
    },
    "win32": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Win%2F1110036%2Fchrome-win.zip?generation=1677368775095363&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Win%2F1110036%2Fchromedriver_win32.zip?generation=1677368988947504&alt=media"
    },
    "mac": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Mac%2F1110036%2Fchrome-mac.zip?generation=1677366724785907&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Mac%2F1110036%2Fchromedriver_mac64.zip?generation=1677366732746411&alt=media"
    },
    "mac_arm": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Mac_Arm%2F1110125%2Fchrome-mac.zip?generation=1677454456625954&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Mac_Arm%2F1110125%2Fchromedriver_mac64.zip?generation=1677454462117324&alt=media"
    },
    "linux_x64": {
        "browser_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Linux_x64%2F1109848%2Fchrome-linux.zip?generation=1677278713298627&alt=media",
        "driver_zip": "https://www.googleapis.com/download/storage/v1/b/chromium-browser-snapshots/o/Linux_x64%2F1109848%2Fchromedriver_linux64.zip?generation=1677278719174033&alt=media"
    }
}

def detect_platform_key():
    system = platform.system()
    arch = platform.architecture()[0]

    if system == "Windows":
        return "win64" if "64" in arch else "win32"
    elif system == "Darwin":
        machine = platform.machine().lower()
        return "mac_arm" if "arm" in machine else "mac"
    elif system == "Linux":
        return "linux_x64"
    else:
        raise Exception("Desteklenmeyen işletim sistemi")

# Telegram ayarları
TELEGRAM_BOT_TOKEN = "**********************************************"
TELEGRAM_CHAT_ID = "615971252"

# Live Feed Ayarları
LIVE_FEED_MAX_EVENTS = 25
LIVE_FEED_DEFAULT_SKELETON_COUNT = 3

# =============================================================================
# LIVE FEED MODULE (live_feed.py içeriği)
# =============================================================================

from collections import deque
import threading

MAX_EVENTS = 25

class LiveFeedManager(QObject):
    feed_updated = pyqtSignal(list)

    def __init__(self):
        super().__init__()
        self.events = deque(maxlen=MAX_EVENTS)
        self.lock = threading.Lock()
        self.thumbnails_to_cleanup = []  # Track thumbnails for cleanup
        self.events_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "live_feed_events.json")
        self.thumbnails_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "live_feed_thumbnails")
        os.makedirs(self.thumbnails_dir, exist_ok=True)
        self.load_events_from_file()

    def add_event(self, event):
        with self.lock:
            # Check if we're at max capacity and need to cleanup old thumbnails
            if len(self.events) >= MAX_EVENTS:
                # Get the oldest event that will be removed
                oldest_event = self.events[-1]
                self._cleanup_event_thumbnail(oldest_event)

            # Add timestamp to event
            event["timestamp"] = time.time()

            # For upload_post events, preserve thumbnail in persistent directory
            if event.get("type") == "upload_post" and event.get("thumb"):
                event["thumb"] = self._preserve_thumbnail_permanently(event["thumb"])

            self.events.appendleft(event)
            self.save_events_to_file()
            self.feed_updated.emit(list(self.events))

    def update_event(self, event_id, updated_event):
        """Update an existing event by ID"""
        with self.lock:
            for i, event in enumerate(self.events):
                if event.get("id") == event_id:
                    # Preserve timestamp from original event
                    if "timestamp" not in updated_event and "timestamp" in event:
                        updated_event["timestamp"] = event["timestamp"]
                    self.events[i] = updated_event
                    self.save_events_to_file()
                    self.feed_updated.emit(list(self.events))
                    return True
            return False

    def register_thumbnail_for_cleanup(self, thumb_path):
        """Register a thumbnail path for cleanup when event expires"""
        if thumb_path:
            with self.lock:
                self.thumbnails_to_cleanup.append(thumb_path)

    def _cleanup_event_thumbnail(self, event):
        """Clean up thumbnail associated with an event"""
        if event and event.get("type") == "upload_post":
            thumb_path = event.get("thumb", "")
            if thumb_path and thumb_path in self.thumbnails_to_cleanup:
                try:
                    if os.path.exists(thumb_path):
                        os.remove(thumb_path)
                        logging.debug(f"Event thumbnail cleaned up: {thumb_path}")
                    self.thumbnails_to_cleanup.remove(thumb_path)
                except Exception as e:
                    logging.debug(f"Event thumbnail cleanup failed: {e}")

    def get_events(self):
        with self.lock:
            return list(self.events)

    def clear_events(self):
        with self.lock:
            # Cleanup all thumbnails when clearing events
            for event in self.events:
                self._cleanup_event_thumbnail(event)
            self.events.clear()
            self.thumbnails_to_cleanup.clear()
            self.save_events_to_file()
            self.feed_updated.emit(list(self.events))

    def is_empty(self):
        with self.lock:
            return len(self.events) == 0

    def _preserve_thumbnail_permanently(self, temp_thumb_path):
        """Copy thumbnail to persistent directory and return new path"""
        if not temp_thumb_path or not os.path.exists(temp_thumb_path):
            return ""

        try:
            import shutil
            # Create unique filename with timestamp
            timestamp = int(time.time() * 1000)  # milliseconds for uniqueness
            filename = f"thumb_{timestamp}_{os.path.basename(temp_thumb_path)}"
            persistent_path = os.path.join(self.thumbnails_dir, filename)

            shutil.copy2(temp_thumb_path, persistent_path)
            logging.debug(f"Thumbnail preserved permanently: {persistent_path}")
            return persistent_path
        except Exception as e:
            logging.error(f"Failed to preserve thumbnail permanently: {e}")
            return temp_thumb_path

    def save_events_to_file(self):
        """Save current events to JSON file"""
        try:
            events_data = {
                "events": list(self.events),
                "thumbnails_to_cleanup": self.thumbnails_to_cleanup,
                "saved_at": time.time()
            }
            with open(self.events_file, 'w', encoding='utf-8') as f:
                json.dump(events_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"Failed to save events to file: {e}")

    def load_events_from_file(self):
        """Load events from JSON file on startup"""
        try:
            if not os.path.exists(self.events_file):
                return

            with open(self.events_file, 'r', encoding='utf-8') as f:
                events_data = json.load(f)

            saved_events = events_data.get("events", [])
            self.thumbnails_to_cleanup = events_data.get("thumbnails_to_cleanup", [])

            # Filter out events older than 24 hours
            current_time = time.time()
            valid_events = []

            for event in saved_events:
                event_time = event.get("timestamp", 0)
                if current_time - event_time < 24 * 3600:  # 24 hours
                    # Verify thumbnail still exists
                    if event.get("type") == "upload_post" and event.get("thumb"):
                        if not os.path.exists(event["thumb"]):
                            event["thumb"] = ""  # Clear missing thumbnail
                    valid_events.append(event)
                else:
                    # Clean up old thumbnail
                    if event.get("type") == "upload_post" and event.get("thumb"):
                        try:
                            if os.path.exists(event["thumb"]):
                                os.remove(event["thumb"])
                        except Exception as e:
                            logging.debug(f"Failed to cleanup old thumbnail: {e}")

            # Load valid events (newest first)
            self.events = deque(valid_events[:MAX_EVENTS], maxlen=MAX_EVENTS)

            logging.info(f"Loaded {len(self.events)} live feed events from file")

        except Exception as e:
            logging.error(f"Failed to load events from file: {e}")
            self.events = deque(maxlen=MAX_EVENTS)

    def cleanup_old_thumbnails(self):
        """Clean up thumbnails older than 24 hours"""
        try:
            current_time = time.time()
            for filename in os.listdir(self.thumbnails_dir):
                filepath = os.path.join(self.thumbnails_dir, filename)
                if os.path.isfile(filepath):
                    # Extract timestamp from filename
                    try:
                        if filename.startswith("thumb_"):
                            timestamp_str = filename.split("_")[1]
                            file_time = int(timestamp_str) / 1000  # Convert from milliseconds
                            if current_time - file_time > 24 * 3600:  # 24 hours
                                os.remove(filepath)
                                logging.debug(f"Cleaned up old thumbnail: {filepath}")
                    except (ValueError, IndexError):
                        # If we can't parse timestamp, check file modification time
                        file_time = os.path.getmtime(filepath)
                        if current_time - file_time > 24 * 3600:
                            os.remove(filepath)
                            logging.debug(f"Cleaned up old thumbnail by mtime: {filepath}")
        except Exception as e:
            logging.error(f"Failed to cleanup old thumbnails: {e}")

# Global live feed manager instance
live_feed_manager = LiveFeedManager()

# =============================================================================
# SETUP CHECKER MODULE (setup_checker.py içeriği)
# =============================================================================

def check_python_version():
    """Check if Python version is compatible"""
    print("Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} is not supported. Please install Python 3.8 or higher.")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def check_required_modules():
    """Check if all required Python modules are installed"""
    print("\nChecking required Python modules...")
    
    required_modules = [
        'PyQt5',
        'PyQt5.QtWebEngineWidgets',
        'selenium',
        'instagrapi',
        'instaloader',
        'yt_dlp',
        'requests',
        'apscheduler',
        'cv2',
        'PIL',
        'psutil',
        'bs4',
        'moviepy'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            import importlib
            importlib.import_module(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - Missing")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ Missing modules: {', '.join(missing_modules)}")
        print("Please run: pip install -r requirements.txt")
        return False
    
    print("✅ All required modules are installed")
    return True

def check_directories():
    """Check if required directories exist and create them if needed"""
    print("\nChecking required directories...")
    
    base_dir = Path(__file__).resolve().parent
    required_dirs = [
        'videos',
        'videos/instagramdownloaded',
        'videos/youtubedownloaded', 
        'videos/twitterdownloaded',
        'videos/temp',
        'configuration',
        'configuration/instagram',
        'configuration/twitter',
        'configuration/instagram/sessions',
        'configuration/twitter/sessions',
        'portable_chromium',
        'live_feed_thumbnails'
    ]
    
    for dir_path in required_dirs:
        full_path = base_dir / dir_path
        if not full_path.exists():
            print(f"📁 Creating directory: {dir_path}")
            full_path.mkdir(parents=True, exist_ok=True)
        else:
            print(f"✅ {dir_path}")
    
    return True

def check_portable_chromium():
    """Check if portable Chromium is available"""
    print("\nChecking Portable Chromium...")
    
    base_dir = Path(__file__).resolve().parent
    chrome_path = base_dir / "portable_chromium" / "browser" / "chrome-win" / "chrome.exe"
    driver_path = base_dir / "portable_chromium" / "driver" / "chromedriver_win32" / "chromedriver.exe"
    
    if chrome_path.exists():
        print("✅ Portable Chrome browser found")
    else:
        print("❌ Portable Chrome browser not found")
        print("The application will attempt to download it on first run")
    
    if driver_path.exists():
        print("✅ ChromeDriver found")
    else:
        print("❌ ChromeDriver not found")
        print("The application will attempt to download it on first run")
    
    return True

def check_ffmpeg():
    """Check if FFmpeg is available"""
    print("\nChecking FFmpeg...")
    
    base_dir = Path(__file__).resolve().parent
    ffmpeg_path = base_dir / "videos" / "ffmpeg" / "ffmpeg.exe"
    
    if ffmpeg_path.exists():
        print("✅ FFmpeg found")
    else:
        print("❌ FFmpeg not found")
        print("The application will download it automatically on first run")
    
    return True

def check_write_permissions():
    """Check if the application has write permissions"""
    print("\nChecking write permissions...")
    
    base_dir = Path(__file__).resolve().parent
    test_file = base_dir / "write_test.tmp"
    
    try:
        with open(test_file, 'w') as f:
            f.write("test")
        test_file.unlink()
        print("✅ Write permissions OK")
        return True
    except Exception as e:
        print(f"❌ Write permission error: {e}")
        print("Please run as administrator or check folder permissions")
        return False

def run_setup_checker():
    """Main setup checker function"""
    print("=" * 50)
    print("SorcerioModules Setup Checker")
    print("=" * 50)
    
    checks = [
        check_python_version(),
        check_required_modules(),
        check_directories(),
        check_portable_chromium(),
        check_ffmpeg(),
        check_write_permissions()
    ]
    
    print("\n" + "=" * 50)
    if all(checks):
        print("✅ All checks passed! SorcerioModules is ready to run.")
        print("You can now run: python main.py")
    else:
        print("❌ Some checks failed. Please fix the issues above before running SorcerioModules.")
        print("\nTo install missing dependencies, run:")
        print("pip install -r requirements.txt")
    print("=" * 50)
    
    return all(checks)

# =============================================================================
# FIRST RUN SETUP MODULE (first_run_setup.py içeriği)
# =============================================================================

def create_first_run_marker():
    """Create a marker file to indicate first run is complete"""
    base_dir = Path(__file__).resolve().parent
    marker_file = base_dir / ".first_run_complete"
    marker_file.touch()

def is_first_run():
    """Check if this is the first run of the application"""
    base_dir = Path(__file__).resolve().parent
    marker_file = base_dir / ".first_run_complete"
    return not marker_file.exists()

def setup_default_configurations():
    """Create default configuration files if they don't exist"""
    base_dir = Path(__file__).resolve().parent
    config_dir = base_dir / "configuration"
    
    # Default profile structure
    default_profile = {
        "username": "",
        "password": "",
        "schedule": {
            "monday": [],
            "tuesday": [],
            "wednesday": [],
            "thursday": [],
            "friday": [],
            "saturday": [],
            "sunday": []
        },
        "hashtags": [],
        "links": [],
        "downloaded": []
    }
    
    # Create Instagram profiles
    instagram_dir = config_dir / "instagram"
    instagram_dir.mkdir(parents=True, exist_ok=True)
    
    for i in range(1, 9):
        profile_file = instagram_dir / f"profile{i}.json"
        if not profile_file.exists():
            with open(profile_file, 'w', encoding='utf-8') as f:
                json.dump(default_profile, f, indent=4, ensure_ascii=False)
    
    # Create Twitter profiles
    twitter_dir = config_dir / "twitter"
    twitter_dir.mkdir(parents=True, exist_ok=True)
    
    for i in range(1, 9):
        profile_file = twitter_dir / f"profile{i}.json"
        if not profile_file.exists():
            with open(profile_file, 'w', encoding='utf-8') as f:
                json.dump(default_profile, f, indent=4, ensure_ascii=False)
    
    # Create session directories
    (instagram_dir / "sessions").mkdir(exist_ok=True)
    (twitter_dir / "sessions").mkdir(exist_ok=True)
    
    # Create statistics file
    stats_file = config_dir / "istatistikler.json"
    if not stats_file.exists():
        default_stats = {
            "instagram": {},
            "twitter": {},
            "last_updated": ""
        }
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(default_stats, f, indent=4, ensure_ascii=False)

def setup_video_directories():
    """Create video storage directories"""
    base_dir = Path(__file__).resolve().parent
    video_dirs = [
        "videos",
        "videos/instagramdownloaded",
        "videos/youtubedownloaded",
        "videos/twitterdownloaded",
        "videos/temp",
        "live_feed_thumbnails"
    ]
    
    for dir_path in video_dirs:
        full_path = base_dir / dir_path
        full_path.mkdir(parents=True, exist_ok=True)

def setup_logging_directory():
    """Ensure logging directory exists"""
    base_dir = Path(__file__).resolve().parent
    videos_dir = base_dir / "videos"
    videos_dir.mkdir(exist_ok=True)

def clean_old_session_files():
    """Clean up old session files that might cause issues"""
    base_dir = Path(__file__).resolve().parent
    
    # Clean old Instagram session files
    instagram_sessions = base_dir / "configuration" / "instagram" / "sessions"
    if instagram_sessions.exists():
        for session_file in instagram_sessions.glob("*.json"):
            # Keep session files but remove any corrupted ones
            try:
                with open(session_file, 'r') as f:
                    json.load(f)
            except (json.JSONDecodeError, Exception):
                session_file.unlink()
                logging.info(f"Removed corrupted session file: {session_file}")

def initialize_live_feed():
    """Initialize live feed events file"""
    base_dir = Path(__file__).resolve().parent
    live_feed_file = base_dir / "live_feed_events.json"
    
    if not live_feed_file.exists():
        initial_events = []
        with open(live_feed_file, 'w', encoding='utf-8') as f:
            json.dump(initial_events, f, indent=2, ensure_ascii=False)

def run_first_run_setup():
    """Run all first-run setup tasks"""
    if not is_first_run():
        return False
    
    print("Running first-time setup...")
    
    try:
        setup_default_configurations()
        setup_video_directories()
        setup_logging_directory()
        clean_old_session_files()
        initialize_live_feed()
        create_first_run_marker()
        
        print("✅ First-time setup completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ First-time setup failed: {e}")
        logging.error(f"First-time setup error: {e}")
        return False

def force_reset_setup():
    """Force reset the first-run setup (for testing)"""
    base_dir = Path(__file__).resolve().parent
    marker_file = base_dir / ".first_run_complete"
    if marker_file.exists():
        marker_file.unlink()
    print("First-run marker removed. Next startup will run first-time setup.")

# =============================================================================
# CLEANUP FOR DISTRIBUTION MODULE (cleanup_for_distribution.py içeriği)
# =============================================================================

def remove_pycache_files(base_dir):
    """Remove all __pycache__ directories and .pyc files"""
    print("Removing Python cache files...")
    
    for root, dirs, files in os.walk(base_dir):
        # Remove __pycache__ directories
        if "__pycache__" in dirs:
            pycache_path = os.path.join(root, "__pycache__")
            print(f"Removing: {pycache_path}")
            shutil.rmtree(pycache_path, ignore_errors=True)
            dirs.remove("__pycache__")
        
        # Remove .pyc files
        for file in files:
            if file.endswith(('.pyc', '.pyo')):
                file_path = os.path.join(root, file)
                print(f"Removing: {file_path}")
                os.remove(file_path)

def remove_ide_files(base_dir):
    """Remove IDE-specific files and directories"""
    print("Removing IDE files...")
    
    ide_patterns = [
        '.idea',
        '.vscode',
        '*.sublime-project',
        '*.sublime-workspace',
        '.project',
        '.pydevproject'
    ]
    
    for pattern in ide_patterns:
        if pattern.startswith('.') and not pattern.endswith('*'):
            # Directory pattern
            ide_path = os.path.join(base_dir, pattern)
            if os.path.exists(ide_path):
                print(f"Removing IDE directory: {ide_path}")
                shutil.rmtree(ide_path, ignore_errors=True)

def remove_virtual_environment(base_dir):
    """Remove virtual environment directory"""
    print("Removing virtual environment...")
    
    venv_patterns = ['.venv', 'venv', 'env']
    
    for pattern in venv_patterns:
        venv_path = os.path.join(base_dir, pattern)
        if os.path.exists(venv_path):
            print(f"Removing virtual environment: {venv_path}")
            shutil.rmtree(venv_path, ignore_errors=True)

def clean_user_data(base_dir):
    """Clean user-specific data that shouldn't be distributed"""
    print("Cleaning user-specific data...")
    
    # Clean session files but keep directory structure
    sessions_dirs = [
        os.path.join(base_dir, "configuration", "instagram", "sessions"),
        os.path.join(base_dir, "configuration", "twitter", "sessions")
    ]
    
    for sessions_dir in sessions_dirs:
        if os.path.exists(sessions_dir):
            for file in os.listdir(sessions_dir):
                if file.endswith(('.json', '.meta.json')):
                    file_path = os.path.join(sessions_dir, file)
                    print(f"Removing session file: {file_path}")
                    os.remove(file_path)
    
    # Clean Chrome profile data
    chrome_profile_dirs = [d for d in os.listdir(base_dir) if d.startswith('chrome_profile_')]
    for chrome_dir in chrome_profile_dirs:
        chrome_path = os.path.join(base_dir, chrome_dir)
        if os.path.isdir(chrome_path):
            print(f"Removing Chrome profile: {chrome_path}")
            shutil.rmtree(chrome_path, ignore_errors=True)
    
    # Clean downloaded content
    videos_dir = os.path.join(base_dir, "videos")
    if os.path.exists(videos_dir):
        content_dirs = ["instagramdownloaded", "twitterdownloaded", "youtubedownloaded"]
        for content_dir in content_dirs:
            content_path = os.path.join(videos_dir, content_dir)
            if os.path.exists(content_path):
                for file in os.listdir(content_path):
                    if file.endswith(('.mp4', '.jpg', '.jpeg', '.png', '.webm')):
                        file_path = os.path.join(content_path, file)
                        print(f"Removing content file: {file_path}")
                        os.remove(file_path)
    
    # Clean log files
    log_files = [
        os.path.join(videos_dir, "social_downloader.log"),
        os.path.join(base_dir, "cleanup.log")
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"Removing log file: {log_file}")
            os.remove(log_file)

def clean_live_feed_data(base_dir):
    """Clean live feed thumbnails and events"""
    print("Cleaning live feed data...")
    
    # Clean live feed thumbnails
    thumbnails_dir = os.path.join(base_dir, "live_feed_thumbnails")
    if os.path.exists(thumbnails_dir):
        for file in os.listdir(thumbnails_dir):
            if file.endswith(('.jpg', '.jpeg', '.png')):
                file_path = os.path.join(thumbnails_dir, file)
                print(f"Removing thumbnail: {file_path}")
                os.remove(file_path)
    
    # Reset live feed events
    events_file = os.path.join(base_dir, "live_feed_events.json")
    if os.path.exists(events_file):
        print(f"Resetting live feed events: {events_file}")
        with open(events_file, 'w', encoding='utf-8') as f:
            f.write('[]')

def reset_configuration_files(base_dir):
    """Reset configuration files to default state"""
    print("Resetting configuration files...")
    
    # Reset statistics
    stats_file = os.path.join(base_dir, "configuration", "istatistikler.json")
    if os.path.exists(stats_file):
        print(f"Resetting statistics: {stats_file}")
        default_stats = {
            "instagram": {},
            "twitter": {},
            "last_updated": ""
        }
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(default_stats, f, indent=4, ensure_ascii=False)

def create_first_run_marker_reset(base_dir):
    """Remove first run marker so setup runs on target system"""
    print("Removing first run marker...")
    
    marker_file = os.path.join(base_dir, ".first_run_complete")
    if os.path.exists(marker_file):
        print(f"Removing first run marker: {marker_file}")
        os.remove(marker_file)

def run_cleanup_for_distribution():
    """Main cleanup function for distribution"""
    base_dir = Path(__file__).resolve().parent
    print(f"Cleaning up SorcerioModules in: {base_dir}")
    print("=" * 50)
    
    try:
        remove_pycache_files(base_dir)
        remove_ide_files(base_dir)
        remove_virtual_environment(base_dir)
        clean_user_data(base_dir)
        clean_live_feed_data(base_dir)
        reset_configuration_files(base_dir)
        create_first_run_marker_reset(base_dir)
        
        print("=" * 50)
        print("✅ Cleanup completed successfully!")
        print("SorcerioModules is now ready for distribution.")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Cleanup failed: {e}")
        logging.error(f"Cleanup error: {e}")

# Live Feed entegrasyonu
feed_emitter.add_item_to_feed_signal.connect(live_feed_manager.add_event)
